name: Tests

on:
  push:
    branches:
      - v1.6
  pull_request:
    branches:
      - v1.6

jobs:
  tests:
    name: PHP ${{ matrix.php-versions }} on ${{ matrix.os }}
    runs-on: ${{ matrix.os }}
    strategy:
      fail-fast: false
      matrix:
        php-versions:
          - '7.1'
          - '7.2'
          - '7.3'
          - '7.4'
          - '8.0'
          - '8.1'
          - '8.2'
          - '8.3'
          - '8.4'
        os: [ubuntu-latest, windows-latest, macOS-latest]

    steps:
      - name: Checkout
        uses: actions/checkout@v2

      - name: Set up PHP ${{ matrix.php-versions }}
        uses: shivammathur/setup-php@v2
        with:
          php-version: ${{ matrix.php-versions }}
          extensions: mbstring
          coverage: pcov

      - name: Setup Problem Matchers for PHP
        run: echo "::add-matcher::${{ runner.tool_cache }}/php.json"

      - name: Setup Problem Matchers for PHPUnit
        run: echo "::add-matcher::${{ runner.tool_cache }}/phpunit.json"

      - name: Validate composer.json and composer.lock
        run: composer validate

      - name: Get Composer Cache Directory
        id: composer-cache
        run: echo "::set-output name=dir::$(composer config cache-files-dir)"

      - name: Cache dependencies
        uses: actions/cache@v2
        with:
          path: ${{ steps.composer-cache.outputs.dir }}
          key: ${{ runner.os }}-composer-${{ hashFiles('**/composer.lock') }}
          restore-keys: ${{ runner.os }}-composer-

      - name: Install dependencies
        run: |
          composer install --prefer-dist --no-progress

      - name: Run test suite
        if: matrix.php-versions == '7.1'
        run: composer run-script test -- --no-coverage

      - name: Run test suite
        if: matrix.php-versions > '7.1'
        run: composer run-script test -- --coverage-text
