==========================
Is it a feature or a bug ?
==========================

Sometimes it's a bit tricky to define if given change proposal or change request is adding new feature or fixing existing issue. This document is providing more clarity about categorisation we use.

Bug
---

Example of bugs:

- crash during application or rule execution
- wrong changes are applied during "fixing codebase" process
- issue with generated report

Feature
-------

Example of features:

- introduction of new rule
- enhancement of existing rule to cover more cases (for example adding support for newly introduced PHP syntax)
- introduction of new ruleset
- update of existing ruleset (for example adjusting it to match newest style of given community or adding newly implemented rule that was supposed to be followed by style of given community, yet not implemented as a rule before)
