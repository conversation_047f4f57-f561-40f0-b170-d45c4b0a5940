<?php

use CodeIgniter\Router\RouteCollection;

/**
 * @var RouteCollection $routes
 */

$routes->get('/user/login', 'User::login');
$routes->post('/user/login', 'User::login');
$routes->get('/user/logout', 'User::logout'); // <-- Tambahan logout di sini

// Halaman utama dan statis
$routes->get('/', 'Home::index');
$routes->get('/about', 'Page::about');
$routes->get('/contact', 'Page::contact');
$routes->post('/contact', 'Page::contact');
$routes->get('/faqs', 'Page::faqs');
$routes->get('/services', 'Page::services');

// Artikel publik (frontend)
$routes->get('/artikel', 'Artikel::index');             // daftar artikel publik

// Debug routes (tanpa auth untuk testing) - HARUS DI ATAS (:any)
$routes->get('artikel/debug_upload', 'Artikel::debug_upload');
$routes->get('artikel/simple_add', 'Artikel::simple_add');
$routes->post('artikel/simple_add', 'Artikel::simple_add');
$routes->get('test/artikel', 'Artikel::test_admin_index');

// Vue.js routes - redirect ke file static di public
$routes->get('vue', function () {
    return redirect()->to(base_url('/vue/'));
});
$routes->get('vuejs', function () {
    return redirect()->to(base_url('/vue/'));
});

// Detail artikel publik - HARUS DI BAWAH route spesifik
$routes->get('/artikel/(:any)', 'Artikel::view/$1');

// REST API routes
$routes->get('post/kategori', 'Post::kategori'); // Harus di atas resource
$routes->resource('post');

// Grup Admin (dengan filter auth)
$routes->group('admin', ['filter' => 'auth'], function ($routes) {

    // Daftar artikel (admin index)
    $routes->get('artikel', 'Artikel::admin_index');

    // Tambah artikel
    $routes->get('artikel/add', 'Artikel::add');    // form tambah
    $routes->post('artikel/add', 'Artikel::add');   // proses tambah

    // Edit artikel
    $routes->get('artikel/edit/(:num)', 'Artikel::edit/$1');    // form edit
    $routes->post('artikel/edit/(:num)', 'Artikel::edit/$1');   // proses edit

    // Hapus artikel
    $routes->get('artikel/delete/(:num)', 'Artikel::delete/$1');
});

// Route untuk AjaxController
$routes->get('/ajax', 'AjaxController::index');
$routes->get('/ajax/getData', 'AjaxController::getData');
$routes->get('/ajax/delete/(:num)', 'AjaxController::delete/$1');
