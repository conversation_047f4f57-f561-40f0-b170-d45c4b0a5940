<?xml version="1.0" encoding="UTF-8"?>
<phpunit backupGlobals="false"
         backupStaticAttributes="false"
         bootstrap="src/test/bootstrap.php"
         colors="true"
         convertErrorsToExceptions="true"
         convertNoticesToExceptions="true"
         convertWarningsToExceptions="true"
         forceCoversAnnotation="false"
         mapTestClassNameToCoveredClassName="false"
         processIsolation="false"
         stopOnError="false"
         stopOnFailure="false"
         stopOnIncomplete="false"
         stopOnSkipped="false"
         verbose="true">
  <testsuites>
    <testsuite>
      <directory suffix=".phpt">./src/test/phpt</directory>
      <directory suffix="TestCase.php">./src/test/php</directory>
    </testsuite>
  </testsuites>

  <filter>
    <whitelist>
      <directory>src/main/php</directory>
    </whitelist>
  </filter>

  <logging>
    <log type="coverage-html" target="docs/coverage" charset="UTF-8"
         yui="true" highlight="false"
         lowUpperBound="35" highLowerBound="70"/>
    <log type="coverage-clover" target="docs/phpunit/clover.xml"/>
    <log type="junit" target="docs/phpunit/junit.xml" logIncompleteSkipped="false"/>
    <log type="testdox-html" target="docs/phpunit/testdox.html"/>
  </logging>

  <php>
    <ini name="memory_limit" value="-1"/>
    <ini name="error_reporting" value="30719"/> <!-- E_ALL | E_STRICT -->
  </php>
</phpunit>
