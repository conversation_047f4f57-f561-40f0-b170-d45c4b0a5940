<?php

declare(strict_types=1);

/*
 * This file is part of PHP CS Fixer.
 *
 * (c) <PERSON><PERSON><PERSON> <<EMAIL>>
 *     <PERSON><PERSON> <<EMAIL>>
 *
 * This source file is subject to the MIT license that is bundled
 * with this source code in the file LICENSE.
 */

namespace PhpCsFixer;

/**
 * <AUTHOR> <<EMAIL>>
 *
 * @internal
 */
final class FixerNameValidator
{
    public function isValid(string $name, bool $isCustom): bool
    {
        if (!$isCustom) {
            return Preg::match('/^[a-z][a-z0-9_]*$/', $name);
        }

        return Preg::match('/^[A-Z][a-zA-Z0-9]*\/[a-z][a-z0-9_]*$/', $name);
    }
}
