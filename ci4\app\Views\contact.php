<?= $this->include('template/header'); ?>

<div class="container mt-4" style="min-height: 70vh;">
    <div class="row">
        <div class="col-md-8">
            <h1><?= $title; ?></h1>
            <p class="lead"><?= $content; ?></p>

            <?php if (isset($success) && $success): ?>
                <div class="alert alert-success alert-dismissible fade show" role="alert">
                    <i class="fas fa-check-circle"></i> <?= $message; ?>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <?php if (isset($error) && $error): ?>
                <div class="alert alert-danger alert-dismissible fade show" role="alert">
                    <i class="fas fa-exclamation-circle"></i> Terdapat kesalahan dalam form:
                    <ul class="mb-0 mt-2">
                        <?php foreach ($errors as $error): ?>
                            <li><?= $error; ?></li>
                        <?php endforeach; ?>
                    </ul>
                    <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                </div>
            <?php endif; ?>

            <div class="card">
                <div class="card-header">
                    <h4>📧 Kirim Pesan</h4>
                </div>
                <div class="card-body">
                    <form action="<?= base_url('/contact'); ?>" method="post">
                        <?= csrf_field(); ?>
                        <div class="mb-3">
                            <label for="nama" class="form-label">Nama Lengkap</label>
                            <input type="text" class="form-control" id="nama" name="nama" value="<?= old('nama'); ?>" required>
                        </div>

                        <div class="mb-3">
                            <label for="email" class="form-label">Email</label>
                            <input type="email" class="form-control" id="email" name="email" value="<?= old('email'); ?>" required>
                        </div>

                        <div class="mb-3">
                            <label for="telepon" class="form-label">Nomor Telepon</label>
                            <input type="tel" class="form-control" id="telepon" name="telepon" value="<?= old('telepon'); ?>">
                        </div>

                        <div class="mb-3">
                            <label for="subjek" class="form-label">Subjek</label>
                            <input type="text" class="form-control" id="subjek" name="subjek" value="<?= old('subjek'); ?>" required>
                        </div>

                        <div class="mb-3">
                            <label for="pesan" class="form-label">Pesan</label>
                            <textarea class="form-control" id="pesan" name="pesan" rows="5" required><?= old('pesan'); ?></textarea>
                        </div>

                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-paper-plane"></i> Kirim Pesan
                        </button>
                    </form>
                </div>
            </div>
        </div>

        <div class="col-md-4">
            <div class="card">
                <div class="card-header">
                    <h4>📍 Informasi Kontak</h4>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <h6><i class="fas fa-map-marker-alt"></i> Alamat</h6>
                        <p class="text-muted">
                            Jl. Contoh No. 123<br>
                            Jakarta Pusat, DKI Jakarta<br>
                            Indonesia 10110
                        </p>
                    </div>

                    <div class="mb-3">
                        <h6><i class="fas fa-phone"></i> Telepon</h6>
                        <p class="text-muted">08777777777</p>
                    </div>

                    <div class="mb-3">
                        <h6><i class="fas fa-envelope"></i> Email</h6>
                        <p class="text-muted"><EMAIL></p>
                    </div>

                    <div class="mb-3">
                        <h6><i class="fas fa-clock"></i> Jam Operasional</h6>
                        <p class="text-muted">
                            Senin - Jumat: 09:00 - 17:00<br>
                            Sabtu: 09:00 - 12:00<br>
                            Minggu: Tutup
                        </p>
                    </div>
                </div>
            </div>

            <div class="card mt-3">
                <div class="card-header">
                    <h4>🌐 Media Sosial</h4>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="#" class="btn btn-outline-primary btn-sm">
                            <i class="fab fa-facebook"></i> Facebook
                        </a>
                        <a href="#" class="btn btn-outline-info btn-sm">
                            <i class="fab fa-twitter"></i> Twitter
                        </a>
                        <a href="#" class="btn btn-outline-danger btn-sm">
                            <i class="fab fa-instagram"></i> Instagram
                        </a>
                        <a href="#" class="btn btn-outline-primary btn-sm">
                            <i class="fab fa-linkedin"></i> LinkedIn
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?= $this->include('template/footer'); ?>