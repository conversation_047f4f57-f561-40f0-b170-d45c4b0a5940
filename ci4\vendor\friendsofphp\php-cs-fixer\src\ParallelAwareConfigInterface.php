<?php

declare(strict_types=1);

/*
 * This file is part of PHP CS Fixer.
 *
 * (c) <PERSON><PERSON><PERSON> <<EMAIL>>
 *     <PERSON><PERSON> <<EMAIL>>
 *
 * This source file is subject to the MIT license that is bundled
 * with this source code in the file LICENSE.
 */

namespace PhpCsFixer;

use PhpCsFixer\Runner\Parallel\ParallelConfig;

/**
 * <AUTHOR> <<EMAIL>>
 *
 * @TODO 4.0 Include parallel runner config in main ConfigInterface
 */
interface ParallelAwareConfigInterface extends ConfigInterface
{
    public function getParallelConfig(): ParallelConfig;

    public function setParallelConfig(ParallelConfig $config): ConfigInterface;
}
