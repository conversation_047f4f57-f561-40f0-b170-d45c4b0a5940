<?php

namespace App\Controllers;

use App\Models\ArtikelModel;

class Home extends BaseController
{
    public function index()
    {
        $title = 'Beranda';
        $model = new ArtikelModel();

        // Ambil semua artikel dengan kategori (HANYA YANG PUBLISH)
        $artikel = $model->select('artikel.*, kategori.nama_kategori')
            ->join('kategori', 'kategori.id_kategori = artikel.id_kategori', 'left')
            ->where('artikel.status', 1) // Hanya artikel yang publish
            ->orderBy('tanggal', 'DESC')
            ->findAll();

        // Ambil 3 artikel terkini dengan kategori (HANYA YANG PUBLISH)
        $artikel_terkini = $model->select('artikel.*, kategori.nama_kategori')
            ->join('kategori', 'kategori.id_kategori = artikel.id_kategori', 'left')
            ->where('artikel.status', 1) // Hanya artikel yang publish
            ->orderBy('tanggal', 'DESC')
            ->findAll(3);

        $kategoriModel = new \App\Models\KategoriModel();
        $kategori = $kategoriModel->findAll();

        // Inisialisasi selected_kategori
        $selected_kategori = $this->request->getGet('kategori') ?? '';

        // Filter berdasarkan kategori jika ada (HANYA YANG PUBLISH)
        if ($selected_kategori) {
            $artikel = $model->select('artikel.*, kategori.nama_kategori')
                ->join('kategori', 'kategori.id_kategori = artikel.id_kategori', 'left')
                ->where('artikel.id_kategori', $selected_kategori)
                ->where('artikel.status', 1) // Hanya artikel yang publish
                ->orderBy('tanggal', 'DESC')
                ->findAll();
        }

        // Kirim ke view
        return view('home', compact('artikel', 'artikel_terkini', 'title', 'kategori', 'selected_kategori'));
    }
}
