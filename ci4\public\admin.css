/* Admin CSS - <PERSON><PERSON><PERSON><PERSON> dari CSS utama */

/* Reset untuk admin pages */
.admin-page {
    font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
    background-color: #f8f9fa;
    margin: 0;
    padding: 0;
}

/* Container admin */
.admin-container {
    max-width: 1200px;
    margin: 0 auto;
    padding: 20px;
}

/* Header admin */
.admin-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1rem 0;
    margin-bottom: 2rem;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
}

.admin-nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    max-width: 1200px;
    margin: 0 auto;
    padding: 0 20px;
}

.admin-nav .logo {
    font-size: 1.5rem;
    font-weight: bold;
    color: white;
    text-decoration: none;
}

.admin-nav .nav-links {
    display: flex;
    gap: 1rem;
}

.admin-nav .nav-links a {
    color: white;
    text-decoration: none;
    padding: 0.5rem 1rem;
    border-radius: 5px;
    transition: background-color 0.3s;
}

.admin-nav .nav-links a:hover,
.admin-nav .nav-links a.active {
    background-color: rgba(255,255,255,0.2);
}

/* Card styling untuk admin */
.admin-card {
    background: white;
    border-radius: 10px;
    box-shadow: 0 2px 10px rgba(0,0,0,0.1);
    margin-bottom: 2rem;
    overflow: hidden;
}

.admin-card-header {
    background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
    color: white;
    padding: 1rem 1.5rem;
    font-weight: 600;
}

.admin-card-body {
    padding: 1.5rem;
}

/* Table styling khusus admin */
.admin-table {
    width: 100%;
    border-collapse: collapse;
    margin-top: 1rem;
}

.admin-table th,
.admin-table td {
    padding: 12px;
    text-align: left;
    border-bottom: 1px solid #dee2e6;
}

.admin-table th {
    background-color: #f8f9fa;
    font-weight: 600;
    color: #495057;
}

.admin-table tbody tr:hover {
    background-color: #f8f9fa;
}

/* Button styling untuk admin */
.admin-btn {
    display: inline-block;
    padding: 8px 16px;
    margin: 2px;
    border: none;
    border-radius: 5px;
    text-decoration: none;
    font-size: 14px;
    cursor: pointer;
    transition: all 0.3s ease;
}

.admin-btn-primary {
    background-color: #007bff;
    color: white;
}

.admin-btn-primary:hover {
    background-color: #0056b3;
    color: white;
}

.admin-btn-success {
    background-color: #28a745;
    color: white;
}

.admin-btn-success:hover {
    background-color: #1e7e34;
    color: white;
}

.admin-btn-danger {
    background-color: #dc3545;
    color: white;
}

.admin-btn-danger:hover {
    background-color: #bd2130;
    color: white;
}

.admin-btn-secondary {
    background-color: #6c757d;
    color: white;
}

.admin-btn-secondary:hover {
    background-color: #545b62;
    color: white;
}

/* Form styling untuk admin */
.admin-form-group {
    margin-bottom: 1.5rem;
}

.admin-form-label {
    display: block;
    margin-bottom: 0.5rem;
    font-weight: 600;
    color: #495057;
}

.admin-form-control {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid #ced4da;
    border-radius: 5px;
    font-size: 14px;
    transition: border-color 0.3s, box-shadow 0.3s;
}

.admin-form-control:focus {
    border-color: #80bdff;
    box-shadow: 0 0 0 0.2rem rgba(0,123,255,.25);
    outline: none;
}

.admin-form-select {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid #ced4da;
    border-radius: 5px;
    background-color: white;
    font-size: 14px;
}

.admin-textarea {
    width: 100%;
    padding: 10px 12px;
    border: 1px solid #ced4da;
    border-radius: 5px;
    font-size: 14px;
    resize: vertical;
    min-height: 120px;
}

/* Alert styling untuk admin */
.admin-alert {
    padding: 12px 16px;
    margin-bottom: 1rem;
    border-radius: 5px;
    border: 1px solid transparent;
}

.admin-alert-success {
    color: #155724;
    background-color: #d4edda;
    border-color: #c3e6cb;
}

.admin-alert-danger {
    color: #721c24;
    background-color: #f8d7da;
    border-color: #f5c6cb;
}

.admin-alert-info {
    color: #0c5460;
    background-color: #d1ecf1;
    border-color: #bee5eb;
}

/* Search form styling */
.admin-search-form {
    display: flex;
    gap: 10px;
    margin-bottom: 1rem;
    flex-wrap: wrap;
}

.admin-search-input {
    flex: 1;
    min-width: 200px;
}

/* Pagination styling untuk admin */
.admin-pagination {
    display: flex;
    justify-content: center;
    margin-top: 2rem;
}

.admin-pagination ul {
    display: flex;
    list-style: none;
    padding: 0;
    margin: 0;
    gap: 5px;
}

.admin-pagination li a,
.admin-pagination li span {
    display: block;
    padding: 8px 12px;
    text-decoration: none;
    color: #007bff;
    border: 1px solid #dee2e6;
    border-radius: 5px;
    transition: all 0.3s;
}

.admin-pagination li.active span {
    background-color: #007bff;
    color: white;
    border-color: #007bff;
}

.admin-pagination li a:hover {
    background-color: #e9ecef;
}

/* Responsive untuk admin */
@media (max-width: 768px) {
    .admin-container {
        padding: 10px;
    }
    
    .admin-nav {
        flex-direction: column;
        gap: 1rem;
    }
    
    .admin-search-form {
        flex-direction: column;
    }
    
    .admin-search-input {
        min-width: auto;
    }
    
    .admin-table {
        font-size: 12px;
    }
    
    .admin-table th,
    .admin-table td {
        padding: 8px;
    }
}

/* Utility classes untuk admin */
.admin-text-center { text-align: center; }
.admin-text-right { text-align: right; }
.admin-mb-1 { margin-bottom: 0.5rem; }
.admin-mb-2 { margin-bottom: 1rem; }
.admin-mb-3 { margin-bottom: 1.5rem; }
.admin-mt-1 { margin-top: 0.5rem; }
.admin-mt-2 { margin-top: 1rem; }
.admin-mt-3 { margin-top: 1.5rem; }
.admin-p-1 { padding: 0.5rem; }
.admin-p-2 { padding: 1rem; }
.admin-p-3 { padding: 1.5rem; }

/* Loading state */
.admin-loading {
    text-align: center;
    padding: 2rem;
    color: #6c757d;
}

.admin-loading i {
    font-size: 2rem;
    margin-bottom: 1rem;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: rotate(0deg); }
    100% { transform: rotate(360deg); }
}
