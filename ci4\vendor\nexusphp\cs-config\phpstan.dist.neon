includes:
  - phpstan-baseline.php
  - vendor/phpstan/phpstan/conf/bleedingEdge.neon

parameters:
  tmpDir: build/phpstan
  level: 10
  paths:
    - src
    - tests
  bootstrapFiles:
    - vendor/autoload.php
  additionalConstructors:
    - Nexus\CsConfig\Test\AbstractCustomFixerTestCase::setUp
  checkTooWideReturnTypesInProtectedAndPublicMethods: true
  checkUninitializedProperties: true
  checkImplicitMixed: true
  checkBenevolentUnionTypes: true
  checkMissingCallableSignature: true
  reportAlwaysTrueInLastCondition: true
  treatPhpDocTypesAsCertain: false
