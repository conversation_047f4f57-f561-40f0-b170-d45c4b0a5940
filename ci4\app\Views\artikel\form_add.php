<?= $this->include('template/admin_header'); ?>

<div class="container-fluid py-4">
    <!-- Header Section -->
    <div class="row mb-4">
        <div class="col-md-8">
            <h1 class="h3 mb-2">
                <i class="fas fa-plus-circle me-2 text-success"></i><?= $title; ?>
            </h1>
            <p class="text-muted">Buat artikel baru dengan mengisi form di bawah ini</p>
        </div>
        <div class="col-md-4 text-md-end">
            <a href="<?= base_url('/admin/artikel'); ?>" class="btn btn-secondary">
                <i class="fas fa-arrow-left me-2"></i>Kembali ke Dashboard
            </a>
        </div>
    </div>

    <!-- Form Section -->
    <div class="row">
        <div class="col-lg-8">
            <div class="card shadow-sm border-0">
                <div class="card-header bg-gradient text-white" style="background: linear-gradient(135deg, #28a745 0%, #20c997 100%);">
                    <h5 class="mb-0">
                        <i class="fas fa-edit me-2"></i>Form Tambah Artikel
                    </h5>
                </div>
                <div class="card-body p-4">
                    <?php if (session()->getFlashdata('errors')): ?>
                        <div class="alert alert-danger alert-dismissible fade show" role="alert">
                            <i class="fas fa-exclamation-triangle me-2"></i>
                            <strong>Terdapat kesalahan:</strong>
                            <ul class="mb-0 mt-2">
                                <?php foreach (session()->getFlashdata('errors') as $error): ?>
                                    <li><?= $error; ?></li>
                                <?php endforeach; ?>
                            </ul>
                            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
                        </div>
                    <?php endif; ?>

                    <form action="" method="post" enctype="multipart/form-data">
                        <?= csrf_field(); ?>

                        <!-- Judul Artikel -->
                        <div class="mb-4">
                            <label for="judul" class="form-label fw-bold">
                                <i class="fas fa-heading me-2 text-primary"></i>Judul Artikel
                            </label>
                            <input type="text" name="judul" id="judul" class="form-control form-control-lg"
                                placeholder="Masukkan judul artikel yang menarik..."
                                value="<?= old('judul'); ?>" required>
                            <div class="form-text">
                                <i class="fas fa-info-circle me-1"></i>
                                Judul yang baik akan menarik pembaca untuk membaca artikel
                            </div>
                        </div>

                        <!-- Kategori -->
                        <div class="mb-4">
                            <label for="id_kategori" class="form-label fw-bold">
                                <i class="fas fa-tags me-2 text-warning"></i>Kategori
                            </label>
                            <select name="id_kategori" id="id_kategori" class="form-select form-select-lg" required>
                                <option value="">🏷️ Pilih Kategori Artikel</option>
                                <?php foreach ($kategori as $k): ?>
                                    <option value="<?= $k['id_kategori']; ?>" <?= old('id_kategori') == $k['id_kategori'] ? 'selected' : ''; ?>>
                                        <?= $k['nama_kategori']; ?>
                                    </option>
                                <?php endforeach; ?>
                            </select>
                        </div>

                        <!-- Gambar Artikel -->
                        <div class="mb-4">
                            <label for="gambar" class="form-label fw-bold">
                                <i class="fas fa-image me-2 text-info"></i>Gambar Artikel
                            </label>
                            <div class="input-group">
                                <span class="input-group-text">
                                    <i class="fas fa-upload"></i>
                                </span>
                                <input type="file" name="gambar" id="gambar" class="form-control" accept="image/*">
                            </div>
                            <div class="form-text">
                                <i class="fas fa-info-circle me-1"></i>
                                Format yang didukung: JPG, PNG, GIF. Maksimal 2MB. (Opsional)
                            </div>
                        </div>

                        <!-- Isi Artikel -->
                        <div class="mb-4">
                            <label for="isi" class="form-label fw-bold">
                                <i class="fas fa-align-left me-2 text-success"></i>Isi Artikel
                            </label>
                            <textarea name="isi" id="isi" rows="12" class="form-control"
                                placeholder="Tulis isi artikel di sini... Gunakan paragraf yang jelas dan mudah dibaca."
                                required><?= old('isi'); ?></textarea>
                            <div class="form-text">
                                <i class="fas fa-info-circle me-1"></i>
                                Minimal 100 karakter untuk artikel yang berkualitas
                            </div>
                        </div>

                        <!-- Submit Button -->
                        <div class="d-grid gap-2 d-md-flex justify-content-md-end">
                            <a href="<?= base_url('/admin/artikel'); ?>" class="btn btn-outline-secondary btn-lg me-md-2">
                                <i class="fas fa-times me-2"></i>Batal
                            </a>
                            <button type="submit" class="btn btn-success btn-lg px-4">
                                <i class="fas fa-save me-2"></i>Simpan Artikel
                            </button>
                        </div>
                    </form>
                </div>
            </div>
        </div>

        <!-- Tips Section -->
        <div class="col-lg-4">
            <div class="card shadow-sm border-0">
                <div class="card-header bg-info text-white">
                    <h6 class="mb-0">
                        <i class="fas fa-lightbulb me-2"></i>Tips Menulis Artikel
                    </h6>
                </div>
                <div class="card-body">
                    <div class="mb-3">
                        <h6 class="text-primary">
                            <i class="fas fa-heading me-2"></i>Judul yang Baik
                        </h6>
                        <small class="text-muted">
                            • Gunakan kata kunci yang relevan<br>
                            • Maksimal 60 karakter<br>
                            • Menarik dan informatif
                        </small>
                    </div>

                    <div class="mb-3">
                        <h6 class="text-warning">
                            <i class="fas fa-image me-2"></i>Gambar Berkualitas
                        </h6>
                        <small class="text-muted">
                            • Resolusi minimal 800x600px<br>
                            • Relevan dengan konten<br>
                            • Format JPG/PNG
                        </small>
                    </div>

                    <div class="mb-3">
                        <h6 class="text-success">
                            <i class="fas fa-paragraph me-2"></i>Konten Berkualitas
                        </h6>
                        <small class="text-muted">
                            • Minimal 300 kata<br>
                            • Gunakan paragraf pendek<br>
                            • Bahasa yang mudah dipahami
                        </small>
                    </div>

                    <div class="alert alert-light border-0">
                        <small class="text-muted">
                            <i class="fas fa-info-circle me-1"></i>
                            <strong>Info:</strong> Artikel akan langsung dipublikasikan setelah disimpan.
                        </small>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<?= $this->include('template/admin_footer'); ?>