<?php

declare(strict_types=1);

/*
 * This file is part of PHP CS Fixer.
 *
 * (c) <PERSON><PERSON><PERSON> <<EMAIL>>
 *     <PERSON><PERSON> <<EMAIL>>
 *
 * This source file is subject to the MIT license that is bundled
 * with this source code in the file LICENSE.
 */

namespace PhpCsFixer\Tokenizer\Transformer;

use PhpCs<PERSON><PERSON>er\Tokenizer\AbstractTransformer;
use PhpCsFixer\Tokenizer\Token;
use <PERSON>p<PERSON>F<PERSON>er\Tokenizer\Tokens;

/**
 * Move trailing whitespaces from comments and docs into following T_WHITESPACE token.
 *
 * <AUTHOR> <<EMAIL>>
 *
 * @internal
 */
final class WhitespacyCommentTransformer extends AbstractTransformer
{
    public function getRequiredPhpVersionId(): int
    {
        return 5_00_00;
    }

    public function process(Tokens $tokens, Token $token, int $index): void
    {
        if (!$token->isComment()) {
            return;
        }

        $content = $token->getContent();
        $trimmedContent = rtrim($content);

        // nothing trimmed, nothing to do
        if ($content === $trimmedContent) {
            return;
        }

        $whitespaces = substr($content, \strlen($trimmedContent));

        $tokens[$index] = new Token([$token->getId(), $trimmedContent]);

        if (isset($tokens[$index + 1]) && $tokens[$index + 1]->isWhitespace()) {
            $tokens[$index + 1] = new Token([T_WHITESPACE, $whitespaces.$tokens[$index + 1]->getContent()]);
        } else {
            $tokens->insertAt($index + 1, new Token([T_WHITESPACE, $whitespaces]));
        }
    }

    public function getCustomTokens(): array
    {
        return [];
    }
}
