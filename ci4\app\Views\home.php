<?= $this->include('template/header'); ?>

<link rel="stylesheet" href="<?= base_url('css/style.css'); ?>">

<!-- Hero Section -->
<div class="hero-section text-white py-5 mb-4 custom-hero" style="background: linear-gradient(135deg, #667eea 0%, #764ba2 100%); position: relative; overflow: hidden; border-radius: 0 0 32px 32px; box-shadow: 0 8px 32px rgba(102, 126, 234, 0.10);">
    <!-- Background Pattern -->
    <div style="position: absolute; top: 0; left: 0; right: 0; bottom: 0; opacity: 0.08; background-image: url('data:image/svg+xml,<svg xmlns=\" http://www.w3.org/2000/svg\" viewBox=\"0 0 100 100\"><circle cx=\"50\" cy=\"50\" r=\"2\" fill=\"white\" /></svg>'); background-size: 50px 50px; z-index: 1;"></div>

    <div class="container position-relative" style="z-index:2;">
        <div class="row align-items-center min-vh-50">
            <div class="col-lg-7">
                <div class="hero-content">
                    <div class="badge bg-warning text-dark mb-3 px-3 py-2 shadow-lg" style="font-size:1.1rem; border-radius:8px;">
                        <i class="fas fa-star me-1"></i> Platform Berita Terpercaya
                    </div>
                    <h1 class="display-3 fw-bold mb-4" style="line-height: 1.2; text-shadow: 0 4px 24px rgba(102,126,234,0.15);">
                        🌟 Selamat Datang di
                        <span class="text-warning" style="text-shadow: 0 2px 8px #fff200;">Eufroshine</span>
                    </h1>
                    <p class="lead mb-4 text-light" style="font-size: 1.25rem; line-height: 1.8; text-shadow: 0 2px 8px rgba(0,0,0,0.08);">
                        Temukan artikel menarik, informasi terkini, dan pengetahuan bermanfaat
                        yang dikurasi khusus untuk Anda. Jelajahi berbagai kategori dan
                        dapatkan wawasan baru setiap hari di <b>Eufroshine</b>.
                    </p>

                    <!-- Stats -->
                    <div class="row mb-4">
                        <div class="col-4">
                            <div class="text-center">
                                <h4 class="fw-bold text-warning mb-1"><?= count($artikel); ?>+</h4>
                                <small class="text-light">Artikel</small>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="text-center">
                                <h4 class="fw-bold text-warning mb-1"><?= count($kategori); ?>+</h4>
                                <small class="text-light">Kategori</small>
                            </div>
                        </div>
                        <div class="col-4">
                            <div class="text-center">
                                <h4 class="fw-bold text-warning mb-1">24/7</h4>
                                <small class="text-light">Update</small>
                            </div>
                        </div>
                    </div>

                    <div class="d-flex flex-wrap gap-3">
                        <a href="<?= base_url('/artikel'); ?>" class="btn btn-warning btn-lg px-4 py-3 fw-bold">
                            <i class="fas fa-newspaper me-2"></i> Jelajahi Artikel
                        </a>
                        <a href="<?= base_url('/about'); ?>" class="btn btn-outline-light btn-lg px-4 py-3">
                            <i class="fas fa-info-circle me-2"></i> Tentang Kami
                        </a>
                    </div>
                </div>
            </div>
            <div class="col-lg-5 text-center">
                <div class="hero-illustration">
                    <div class="position-relative">
                        <!-- Main Icon -->
                        <div class="bg-white bg-opacity-10 rounded-circle p-5 d-inline-block mb-3" style="backdrop-filter: blur(10px);">
                            <i class="fas fa-newspaper fa-5x text-warning"></i>
                        </div>

                        <!-- Floating Elements -->
                        <div class="position-absolute top-0 start-0 bg-warning rounded-circle p-3" style="animation: float 3s ease-in-out infinite;">
                            <i class="fas fa-pen-fancy text-dark"></i>
                        </div>
                        <div class="position-absolute top-0 end-0 bg-light rounded-circle p-3" style="animation: float 3s ease-in-out infinite 1s;">
                            <i class="fas fa-search text-primary"></i>
                        </div>
                        <div class="position-absolute bottom-0 start-50 translate-middle-x bg-success rounded-circle p-3" style="animation: float 3s ease-in-out infinite 2s;">
                            <i class="fas fa-share-alt text-white"></i>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<style>
    @keyframes float {

        0%,
        100% {
            transform: translateY(0px);
        }

        50% {
            transform: translateY(-10px);
        }
    }

    .min-vh-50 {
        min-height: 50vh;
    }
</style>

<div class="container mt-4" style="min-height: 70vh;">
    <!-- Filter Kategori -->
    <div class="filter-section mb-4 p-3 bg-light rounded">
        <form action="" method="get" class="row g-2 align-items-center">
            <div class="col-md-auto">
                <select name="kategori" class="form-select" style="min-width: 200px;">
                    <option value="">Semua Kategori</option>
                    <?php foreach ($kategori as $k): ?>
                        <option value="<?= $k['id_kategori']; ?>" <?= ($selected_kategori == $k['id_kategori']) ? 'selected' : ''; ?>>
                            <?= $k['nama_kategori']; ?>
                        </option>
                    <?php endforeach; ?>
                </select>
            </div>
            <div class="col-md-auto">
                <button type="submit" class="btn btn-primary">Filter</button>
            </div>
        </form>
    </div>

    <!-- ARTIKEL TERKINI -->
    <div class="artikel-terkini mb-4 p-3 bg-light rounded">
        <h3 class="h5 mb-3">Artikel Terkini</h3>
        <?= view_cell('App\Cells\ArtikelTerkini::index') ?>
    </div>

    <hr class="divider" />

    <!-- MAIN CONTENT WITH SIDEBAR -->
    <div class="row">
        <!-- Main Content -->
        <div class="col-md-8">
            <h2 class="h4 mb-4">📰 Artikel Terbaru</h2>
            <div class="row">
                <?php if (!empty($artikel)) : ?>
                    <?php foreach ($artikel as $row) : ?>
                        <div class="col-md-6 mb-4">
                            <article class="entry card shadow-sm h-100">
                                <?php if (!empty($row['gambar'])): ?>
                                    <img src="<?= base_url('/gambar/' . $row['gambar']); ?>"
                                        alt="<?= esc($row['judul']); ?>"
                                        class="card-img-top"
                                        style="height: 200px; object-fit: cover;">
                                <?php else: ?>
                                    <div class="card-img-top bg-light d-flex align-items-center justify-content-center"
                                        style="height: 200px;">
                                        <i class="fas fa-image text-muted fa-2x"></i>
                                    </div>
                                <?php endif; ?>
                                <div class="card-body d-flex flex-column">
                                    <h5 class="card-title mb-3">
                                        <a href="<?= base_url('/artikel/' . $row['slug']); ?>" class="text-decoration-none text-dark">
                                            <?= esc($row['judul']); ?>
                                        </a>
                                    </h5>
                                    <div class="kategori-badge mb-3">
                                        <span class="badge bg-primary">
                                            <i class="fas fa-tag"></i> <?= esc($row['nama_kategori'] ?? 'Tidak ada kategori'); ?>
                                        </span>
                                    </div>
                                    <p class="card-text text-muted mb-3">
                                        <?= esc(substr(strip_tags($row['isi']), 0, 120)); ?>...
                                    </p>
                                    <div class="mt-auto">
                                        <div class="d-flex justify-content-between align-items-center">
                                            <small class="text-muted">
                                                <i class="fas fa-calendar-alt"></i>
                                                <?= date('d M Y', strtotime($row['tanggal'] ?? date('Y-m-d'))); ?>
                                            </small>
                                            <a href="<?= base_url('/artikel/' . $row['slug']); ?>" class="btn btn-primary btn-sm">
                                                <i class="fas fa-arrow-right"></i> Baca
                                            </a>
                                        </div>
                                    </div>
                                </div>
                            </article>
                        </div>
                    <?php endforeach; ?>
                <?php else : ?>
                    <div class="col-12">
                        <div class="text-center py-5">
                            <i class="fas fa-newspaper fa-4x text-muted mb-3"></i>
                            <h4 class="text-muted">Belum ada artikel</h4>
                            <p class="text-muted">Belum ada artikel yang dipublikasikan.</p>
                        </div>
                    </div>
                <?php endif; ?>
            </div>

            <!-- View All Button -->
            <?php if (!empty($artikel)): ?>
                <div class="text-center mt-4">
                    <a href="<?= base_url('/artikel'); ?>" class="btn btn-outline-primary btn-lg">
                        <i class="fas fa-newspaper me-2"></i> Lihat Semua Artikel
                    </a>
                </div>
            <?php endif; ?>
        </div>

        <!-- Sidebar -->
        <div class="col-md-4">
            <!-- Search Widget -->
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-primary text-white">
                    <h5 class="mb-0"><i class="fas fa-search me-2"></i> Pencarian</h5>
                </div>
                <div class="card-body">
                    <form action="<?= base_url('/artikel'); ?>" method="get">
                        <div class="input-group">
                            <input type="text" name="search" class="form-control" placeholder="Cari artikel...">
                            <button class="btn btn-primary" type="submit">
                                <i class="fas fa-search"></i>
                            </button>
                        </div>
                    </form>
                </div>
            </div>

            <!-- Categories Widget -->
            <div class="card shadow-sm mb-4">
                <div class="card-header bg-success text-white">
                    <h5 class="mb-0"><i class="fas fa-tags me-2"></i> Kategori</h5>
                </div>
                <div class="card-body">
                    <?php if (!empty($kategori)): ?>
                        <div class="list-group list-group-flush">
                            <?php foreach ($kategori as $k): ?>
                                <a href="<?= base_url('/artikel?kategori=' . $k['id_kategori']); ?>"
                                    class="list-group-item list-group-item-action d-flex justify-content-between align-items-center">
                                    <span><i class="fas fa-tag text-success me-2"></i><?= esc($k['nama_kategori']); ?></span>
                                    <span class="badge bg-success rounded-pill">
                                        <?php
                                        // Hitung jumlah artikel per kategori
                                        $count = 0;
                                        foreach ($artikel as $art) {
                                            if ($art['id_kategori'] == $k['id_kategori']) {
                                                $count++;
                                            }
                                        }
                                        echo $count;
                                        ?>
                                    </span>
                                </a>
                            <?php endforeach; ?>
                        </div>
                    <?php else: ?>
                        <p class="text-muted mb-0">Belum ada kategori.</p>
                    <?php endif; ?>
                </div>
            </div>

            <!-- Quick Links Widget -->
            <div class="card shadow-sm">
                <div class="card-header bg-info text-white">
                    <h5 class="mb-0"><i class="fas fa-link me-2"></i> Menu Cepat</h5>
                </div>
                <div class="card-body">
                    <div class="d-grid gap-2">
                        <a href="<?= base_url('/artikel'); ?>" class="btn btn-outline-primary btn-sm">
                            <i class="fas fa-newspaper me-2"></i> Semua Artikel
                        </a>
                        <a href="<?= base_url('/about'); ?>" class="btn btn-outline-success btn-sm">
                            <i class="fas fa-info-circle me-2"></i> Tentang Kami
                        </a>
                        <a href="<?= base_url('/contact'); ?>" class="btn btn-outline-warning btn-sm">
                            <i class="fas fa-envelope me-2"></i> Hubungi Kami
                        </a>
                    </div>
                </div>
            </div>
        </div>
    </div>

    <?= $this->include('template/footer'); ?>