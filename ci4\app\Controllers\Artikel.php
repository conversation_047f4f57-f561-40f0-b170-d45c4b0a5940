<?php

namespace App\Controllers;

use App\Models\ArtikelModel;
use App\Models\KategoriModel;
use CodeIgniter\Exceptions\PageNotFoundException;

class Artikel extends BaseController
{
    // Halaman User - List Artikel
    public function index()
    {
        $title = 'Daftar Artikel';
        $model = new ArtikelModel();
        $kategoriModel = new KategoriModel();

        // Get filter dan search dari query string
        $selected_kategori = $this->request->getVar('kategori') ?? '';
        $search = $this->request->getVar('search') ?? '';

        // Query artikel dengan join kategori (HANYA YANG PUBLISH)
        $query = $model->select('artikel.*, kategori.nama_kategori')
            ->join('kategori', 'kategori.id_kategori = artikel.id_kategori', 'left')
            ->where('artikel.status', 1); // Hanya artikel yang publish

        // Apply filter kategori jika dipilih
        if (!empty($selected_kategori)) {
            $query->where('artikel.id_kategori', $selected_kategori);
        }

        // Apply search jika ada
        if (!empty($search)) {
            $query->groupStart()
                ->like('artikel.judul', $search)
                ->orLike('artikel.isi', $search)
                ->groupEnd();
        }

        $artikel = $query->orderBy('artikel.tanggal', 'DESC')->findAll();
        $kategori = $kategoriModel->findAll();

        $data = [
            'artikel' => $artikel,
            'title' => $title,
            'kategori' => $kategori,
            'selected_kategori' => $selected_kategori,
            'search' => $search
        ];

        return view('artikel/index', $data);
    }

    // Halaman User - Detail Artikel
    public function view($slug)
    {
        $model = new ArtikelModel();
        $artikel = $model->select('artikel.*, kategori.nama_kategori')
            ->join('kategori', 'kategori.id_kategori = artikel.id_kategori', 'left')
            ->where('artikel.slug', $slug)
            ->first();

        if (!$artikel) {
            throw PageNotFoundException::forPageNotFound();
        }

        $title = $artikel['judul'];
        $kategoriModel = new \App\Models\KategoriModel();
        $kategori = $kategoriModel->find($artikel['id_kategori']);

        return view('artikel/detail', compact('artikel', 'title', 'kategori'));
    }

    // Method test untuk AJAX (tanpa authentication)
    public function test_admin_index()
    {
        $title = 'Test AJAX - Daftar Artikel (Admin)';
        $model = new ArtikelModel();
        $q = $this->request->getVar('q') ?? '';
        $kategori_id = $this->request->getVar('kategori_id') ?? '';
        $page = $this->request->getVar('page') ?? 1;

        $builder = $model->table('artikel')
            ->select('artikel.*, kategori.nama_kategori')
            ->join('kategori', 'kategori.id_kategori = artikel.id_kategori', 'left');

        if ($q != '') {
            $builder->like('artikel.judul', $q);
        }
        if ($kategori_id != '') {
            $builder->where('artikel.id_kategori', $kategori_id);
        }

        $artikel = $builder->paginate(10, 'default', $page);
        $pager = $model->pager;

        $data = [
            'title' => $title,
            'q' => $q,
            'kategori_id' => $kategori_id,
            'artikel' => $artikel,
            'pager' => $pager
        ];

        if ($this->request->isAJAX()) {
            return $this->response->setJSON($data);
        } else {
            $kategoriModel = new KategoriModel();
            $data['kategori'] = $kategoriModel->findAll();
            return view('artikel/test_admin_index', $data);
        }
    }

    // Halaman Admin - List Artikel dengan Pagination dan Pencarian
    public function admin_index()
    {
        $title = 'Daftar Artikel (Admin)';
        $model = new ArtikelModel();
        $kategoriModel = new KategoriModel();

        $q = $this->request->getVar('q') ?? '';
        $kategori_id = $this->request->getVar('kategori_id') ?? '';
        $page = $this->request->getVar('page') ?? 1;

        // Query dengan join kategori
        $builder = $model->table('artikel')
            ->select('artikel.*, kategori.nama_kategori')
            ->join('kategori', 'kategori.id_kategori = artikel.id_kategori', 'left')
            ->orderBy('artikel.id', 'DESC');

        // Apply filters
        if (!empty($q)) {
            $builder->like('artikel.judul', $q);
        }
        if (!empty($kategori_id)) {
            $builder->where('artikel.id_kategori', $kategori_id);
        }

        // Get paginated results
        $artikel = $builder->paginate(10, 'default', $page);
        $pager = $model->pager;

        $data = [
            'title' => $title,
            'q' => $q,
            'kategori_id' => $kategori_id,
            'artikel' => $artikel,
            'pager' => $pager
        ];

        // Jika AJAX request, return JSON
        if ($this->request->isAJAX()) {
            return $this->response->setJSON($data);
        }

        // Jika bukan AJAX, return view dengan kategori
        $data['kategori'] = $kategoriModel->findAll();
        return view('artikel/admin_index', $data);
    }

    // Halaman Admin - Tambah Artikel
    public function add()
    {
        $title = "Tambah Artikel";
        $kategoriModel = new KategoriModel();
        $kategori = $kategoriModel->findAll();

        // Jika form disubmit
        if (strtolower($this->request->getMethod()) === 'post') {
            log_message('info', 'Form submitted - POST data: ' . json_encode($this->request->getPost()));

            // Validasi data dengan file upload - LEBIH LONGGAR
            $validation = \Config\Services::validation();
            $validation->setRules([
                'judul' => 'required|min_length[3]',
                'isi' => 'required|min_length[5]',
                'id_kategori' => 'required'
                // Hapus validasi gambar dari sini, kita handle manual
            ]);

            $isDataValid = $validation->withRequest($this->request)->run();

            if (!$isDataValid) {
                log_message('error', 'Validation failed: ' . json_encode($validation->getErrors()));
                session()->setFlashdata('errors', $validation->getErrors());
                return view('artikel/form_add', compact('title', 'kategori'));
            }

            // Proses insert jika validasi berhasil
            $file = $this->request->getFile('gambar');
            $gambarName = '';

            // Handle file upload dengan error handling
            if ($file && $file->getName() != '') {
                log_message('info', 'File detected: ' . $file->getName());
                log_message('info', 'File size: ' . $file->getSize() . ' bytes');
                log_message('info', 'File type: ' . $file->getMimeType());

                // Cek ukuran file (2MB = 2048KB = 2097152 bytes)
                if ($file->getSize() > 2097152) {
                    log_message('error', 'File too large: ' . $file->getSize() . ' bytes (' . round($file->getSize() / 1024, 2) . ' KB)');
                    session()->setFlashdata('errors', ['File gambar terlalu besar! Ukuran: ' . round($file->getSize() / 1024, 2) . ' KB. Maksimal 2MB (2048 KB).']);
                    return view('artikel/form_add', compact('title', 'kategori'));
                }

                // Cek file terlalu kecil (minimal 100 bytes)
                if ($file->getSize() < 100) {
                    log_message('error', 'File too small: ' . $file->getSize() . ' bytes');
                    session()->setFlashdata('errors', ['File gambar terlalu kecil! Ukuran: ' . $file->getSize() . ' bytes. Minimal 100 bytes.']);
                    return view('artikel/form_add', compact('title', 'kategori'));
                }

                if ($file->isValid() && !$file->hasMoved()) {
                    $gambarName = $file->getRandomName();
                    $uploadPath = FCPATH . 'gambar';

                    log_message('info', 'Upload path: ' . $uploadPath);
                    log_message('info', 'File name: ' . $gambarName);

                    if ($file->move($uploadPath, $gambarName)) {
                        log_message('info', 'File uploaded successfully: ' . $gambarName);
                    } else {
                        log_message('error', 'File upload failed: ' . $file->getErrorString());
                        session()->setFlashdata('errors', ['Gagal upload gambar: ' . $file->getErrorString()]);
                        return view('artikel/form_add', compact('title', 'kategori'));
                    }
                } else {
                    $errorMsg = 'File tidak valid';
                    if ($file->getError() == UPLOAD_ERR_INI_SIZE) {
                        $errorMsg = 'File terlalu besar! Maksimal 2MB.';
                    } elseif ($file->getError() == UPLOAD_ERR_FORM_SIZE) {
                        $errorMsg = 'File melebihi batas ukuran form.';
                    } elseif ($file->getError() == UPLOAD_ERR_NO_TMP_DIR) {
                        $errorMsg = 'Folder temporary tidak ditemukan.';
                    }

                    log_message('error', 'File upload error: ' . $errorMsg . ' (Error code: ' . $file->getError() . ')');
                    session()->setFlashdata('errors', [$errorMsg]);
                    return view('artikel/form_add', compact('title', 'kategori'));
                }
            } else {
                log_message('info', 'No file uploaded');
            }

            $artikel = new ArtikelModel();
            $data = [
                'judul' => $this->request->getPost('judul'),
                'isi' => $this->request->getPost('isi'),
                'slug' => url_title($this->request->getPost('judul')),
                'id_kategori' => $this->request->getPost('id_kategori'),
                'status' => 1, // Published by default
                'tanggal' => date('Y-m-d H:i:s'),
                'gambar' => $gambarName,
            ];
            $insertResult = $artikel->insert($data);

            log_message('info', 'Insert result: ' . ($insertResult ? 'SUCCESS' : 'FAILED'));
            log_message('info', 'Data inserted: ' . json_encode($data));

            if ($insertResult) {
                session()->setFlashdata('success', 'Artikel berhasil ditambahkan!');
                return redirect()->to(base_url('/admin/artikel'));
            } else {
                $errors = $artikel->errors();
                log_message('error', 'Database errors: ' . json_encode($errors));
                session()->setFlashdata('errors', ['Gagal menyimpan artikel ke database: ' . implode(', ', $errors)]);
                return view('artikel/form_add', compact('title', 'kategori'));
            }
        }

        return view('artikel/form_add', compact('title', 'kategori'));
    }

    // Halaman Admin - Edit Artikel
    public function edit($id)
    {
        $artikel = new ArtikelModel();
        $kategoriModel = new KategoriModel();

        $validation = \Config\Services::validation();
        $validation->setRules([
            'judul' => 'required',
            'isi'   => 'required',
            'id_kategori' => 'required|integer'
        ]);

        $isDataValid = $validation->withRequest($this->request)->run();

        if ($isDataValid) {
            $data = [
                'judul' => $this->request->getPost('judul'),
                'isi'   => $this->request->getPost('isi'),
                'id_kategori' => $this->request->getPost('id_kategori')
            ];

            // Handle file upload
            if ($file = $this->request->getFile('gambar')) {
                if ($file->isValid() && !$file->hasMoved()) {
                    // Get old image
                    $oldData = $artikel->find($id);
                    $oldImage = $oldData['gambar'] ?? null;

                    // Upload new image with random name
                    $gambarName = $file->getRandomName();
                    $uploadPath = FCPATH . 'gambar';

                    log_message('info', 'Edit - Upload path: ' . $uploadPath);
                    log_message('info', 'Edit - File name: ' . $gambarName);

                    if ($file->move($uploadPath, $gambarName)) {
                        $data['gambar'] = $gambarName;
                        log_message('info', 'Edit - File uploaded successfully: ' . $gambarName);

                        // Delete old image if exists
                        if ($oldImage && file_exists($uploadPath . '/' . $oldImage)) {
                            unlink($uploadPath . '/' . $oldImage);
                            log_message('info', 'Edit - Old image deleted: ' . $oldImage);
                        }
                    } else {
                        log_message('error', 'Edit - File upload failed: ' . $file->getErrorString());
                    }
                } else {
                    log_message('info', 'Edit - No file uploaded or file invalid');
                }
            }

            $artikel->update($id, $data);
            return redirect()->to(base_url('/admin/artikel'));
        }

        $data = $artikel->where('id', $id)->first();
        $kategori = $kategoriModel->findAll();

        $title = "Edit Artikel";

        return view('artikel/form_edit', compact('title', 'data', 'kategori'));
    }

    // Halaman Admin - Delete Artikel
    public function delete($id)
    {
        $artikel = new ArtikelModel();
        $artikel->delete($id);

        return redirect()->to(base_url('/admin/artikel'));
    }

    // Fungsi untuk menampilkan artikel berdasarkan kategori (opsional - sesuai tugas)
    public function kategori($slug_kategori)
    {
        $kategoriModel = new KategoriModel();
        $kategori = $kategoriModel->where('slug_kategori', $slug_kategori)->first();

        if (!$kategori) {
            throw PageNotFoundException::forPageNotFound();
        }

        $title = 'Artikel Kategori: ' . $kategori['nama_kategori'];

        $artikelModel = new ArtikelModel();
        $artikel = $artikelModel->select('artikel.*, kategori.nama_kategori')
            ->join('kategori', 'kategori.id_kategori = artikel.id_kategori')
            ->where('kategori.slug_kategori', $slug_kategori)
            ->findAll();

        return view('artikel/index', compact('artikel', 'title'));
    }

    // Test method untuk debug add artikel
    public function test_add()
    {
        $kategoriModel = new KategoriModel();
        $kategori = $kategoriModel->findAll();

        return view('artikel/test_add', compact('kategori'));
    }

    // Debug method untuk test upload path
    public function debug_upload()
    {
        $uploadPath = WRITEPATH . '../public/gambar';
        $publicPath = FCPATH . 'gambar';

        echo "<h3>Debug Upload Path</h3>";
        echo "<p><strong>WRITEPATH:</strong> " . WRITEPATH . "</p>";
        echo "<p><strong>FCPATH:</strong> " . FCPATH . "</p>";
        echo "<p><strong>Upload Path 1:</strong> " . $uploadPath . "</p>";
        echo "<p><strong>Upload Path 2:</strong> " . $publicPath . "</p>";
        echo "<p><strong>Path 1 exists:</strong> " . (is_dir($uploadPath) ? 'YES' : 'NO') . "</p>";
        echo "<p><strong>Path 2 exists:</strong> " . (is_dir($publicPath) ? 'YES' : 'NO') . "</p>";
        echo "<p><strong>Path 1 writable:</strong> " . (is_writable($uploadPath) ? 'YES' : 'NO') . "</p>";
        echo "<p><strong>Path 2 writable:</strong> " . (is_writable($publicPath) ? 'YES' : 'NO') . "</p>";

        // List files in gambar folder
        if (is_dir($publicPath)) {
            echo "<h4>Files in gambar folder:</h4>";
            $files = scandir($publicPath);
            foreach ($files as $file) {
                if ($file != '.' && $file != '..') {
                    echo "<p>- " . $file . "</p>";
                }
            }
        }
    }

    // Simple add method untuk debug
    public function simple_add()
    {
        log_message('info', 'SIMPLE_ADD: Method called');
        log_message('info', 'SIMPLE_ADD: Request method = ' . $this->request->getMethod());

        $kategoriModel = new KategoriModel();
        $kategori = $kategoriModel->findAll();
        $title = "Simple Add Artikel";

        if (strtolower($this->request->getMethod()) === 'post') {
            log_message('info', 'SIMPLE_ADD: POST request detected');
            log_message('info', 'SIMPLE_ADD: POST data = ' . json_encode($this->request->getPost()));
            $artikel = new ArtikelModel();
            $data = [
                'judul' => $this->request->getPost('judul'),
                'isi' => $this->request->getPost('isi'),
                'slug' => url_title($this->request->getPost('judul')),
                'id_kategori' => $this->request->getPost('id_kategori'),
                'status' => 1,
                'tanggal' => date('Y-m-d H:i:s'),
                'gambar' => '',
            ];

            log_message('info', 'SIMPLE_ADD: Attempting to insert data = ' . json_encode($data));

            if ($artikel->insert($data)) {
                log_message('info', 'SIMPLE_ADD: Insert SUCCESS');
                echo "<h3>SUCCESS!</h3>";
                echo "<p>Data berhasil disimpan!</p>";
                echo "<a href='" . base_url('/admin/artikel') . "'>Lihat Artikel</a>";
            } else {
                log_message('error', 'SIMPLE_ADD: Insert FAILED = ' . json_encode($artikel->errors()));
                echo "<h3>ERROR!</h3>";
                echo "<pre>" . print_r($artikel->errors(), true) . "</pre>";
            }
            return;
        }

        log_message('info', 'SIMPLE_ADD: Showing form view');
        return view('artikel/form_add', compact('title', 'kategori'));
    }
}
