<?php

declare(strict_types=1);

/*
 * This file is part of PHP CS Fixer.
 *
 * (c) <PERSON><PERSON><PERSON> <<EMAIL>>
 *     <PERSON><PERSON> <<EMAIL>>
 *
 * This source file is subject to the MIT license that is bundled
 * with this source code in the file LICENSE.
 */

namespace PhpCsFixer;

/**
 * <AUTHOR> <<EMAIL>>
 *
 * @internal
 */
final class Hasher
{
    private function __construct()
    {
        // cannot create instance of util. class
    }

    /**
     * @return non-empty-string
     */
    public static function calculate(string $code): string
    {
        return \PHP_VERSION_ID >= 8_01_00
            ? hash('xxh128', $code)
            : md5($code);
    }
}
