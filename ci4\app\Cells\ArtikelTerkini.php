<?php

namespace App\Cells;

use App\Models\ArtikelModel;

class ArtikelTerkini
{
    public function index()
    {
        $model = new ArtikelModel();
        $data['artikel'] = $model->select('artikel.*, kategori.nama_kategori')
            ->join('kategori', 'kategori.id_kategori = artikel.id_kategori', 'left')
            ->where('artikel.status', 1) // HANYA YANG PUBLISH
            ->orderBy('artikel.tanggal', 'DESC')
            ->findAll(6);

        return view('components/artikel_terkini', $data);
    }
}
