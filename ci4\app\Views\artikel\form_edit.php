<?= $this->include('template/admin_header'); ?>

<h2><?= $title; ?></h2>

<div class="form-container">
    <form action="" method="post" enctype="multipart/form-data" class="form-tambah">
        <div class="form-group">
            <label for="judul"><PERSON><PERSON><PERSON></label>
            <input type="text" name="judul" id="judul" class="input" placeholder="Masukkan judul artikel..." value="<?= $data['judul']; ?>" required>
        </div>

        <div class="form-group">
            <label for="isi">Isi Artikel</label>
            <textarea name="isi" id="isi" cols="50" rows="10" class="area" placeholder="Tulis isi artikel di sini..." required><?= $data['isi']; ?></textarea>
        </div>

        <div class="form-group">
            <label for="id_kategori">Kategori</label>
            <select name="id_kategori" id="id_kategori" class="input" required>
                <?php foreach ($kategori as $k): ?>
                    <option value="<?= $k['id_kategori']; ?>" <?= ($data['id_kategori'] == $k['id_kategori']) ? 'selected' : ''; ?>>
                        <?= $k['nama_kategori']; ?>
                    </option>
                <?php endforeach; ?>
            </select>
        </div>

        <div class="form-group">
            <label for="gambar">Gambar Artikel</label>
            <?php if (!empty($data['gambar'])): ?>
                <p>Gambar saat ini: <img src="<?= base_url('/gambar/' . $data['gambar']); ?>" alt="Gambar saat ini" style="max-width: 200px;"></p>
            <?php endif; ?>
            <p>
                <input type="file" name="gambar">
                <small>Biarkan kosong jika tidak ingin mengubah gambar</small>
            </p>
        </div>

        <div class="form-group">
            <button type="submit" class="btn btn-large btn-primary">Update Artikel</button>
        </div>
    </form>
</div>

<?= $this->include('template/admin_footer'); ?>