<?= $this->include('template/header'); ?>

<div class="container mt-4" style="min-height: 70vh;">
    <!-- Header Section -->
    <div class="row mb-4">
        <div class="col-md-8">
            <h1 class="h2 mb-2">📰 <?= $title; ?></h1>
            <p class="text-muted">Temukan artikel menarik dan terbaru di sini</p>
        </div>
        <div class="col-md-4 text-md-end">
            <!-- Tombol Tambah Artikel (Untuk Admin) -->
            <?php if (session()->get('logged_in')): ?>
                <a href="<?= base_url('/admin/artikel/add'); ?>" class="btn btn-primary">
                    <i class="fas fa-plus"></i> Tambah Artikel
                </a>
            <?php endif; ?>
        </div>
    </div>

    <!-- Search Section -->
    <div class="row mb-4">
        <div class="col-12">
            <div class="card shadow-sm">
                <div class="card-body">
                    <form action="<?= base_url('/artikel'); ?>" method="get" class="row g-3">
                        <div class="col-md-6">
                            <div class="input-group">
                                <span class="input-group-text"><i class="fas fa-search"></i></span>
                                <input type="text" name="search" class="form-control"
                                    placeholder="Cari artikel berdasarkan judul atau konten..."
                                    value="<?= esc($search ?? ''); ?>">
                            </div>
                        </div>
                        <div class="col-md-4">
                            <select name="kategori" class="form-select">
                                <option value="">Semua Kategori</option>
                                <?php if (!empty($kategori)): ?>
                                    <?php foreach ($kategori as $k): ?>
                                        <option value="<?= $k['id_kategori']; ?>" <?= ($selected_kategori == $k['id_kategori']) ? 'selected' : ''; ?>>
                                            <?= esc($k['nama_kategori']); ?>
                                        </option>
                                    <?php endforeach; ?>
                                <?php endif; ?>
                            </select>
                        </div>
                        <div class="col-md-2">
                            <button type="submit" class="btn btn-primary w-100">
                                <i class="fas fa-search"></i> Cari
                            </button>
                        </div>
                        <?php if (!empty($search) || !empty($selected_kategori)): ?>
                            <div class="col-12">
                                <div class="d-flex align-items-center">
                                    <span class="text-muted me-2">Filter aktif:</span>
                                    <?php if (!empty($search)): ?>
                                        <span class="badge bg-info me-2">
                                            <i class="fas fa-search"></i> "<?= esc($search); ?>"
                                        </span>
                                    <?php endif; ?>
                                    <?php if (!empty($selected_kategori)): ?>
                                        <?php
                                        $kategori_name = '';
                                        foreach ($kategori as $k) {
                                            if ($k['id_kategori'] == $selected_kategori) {
                                                $kategori_name = $k['nama_kategori'];
                                                break;
                                            }
                                        }
                                        ?>
                                        <span class="badge bg-success me-2">
                                            <i class="fas fa-tag"></i> <?= esc($kategori_name); ?>
                                        </span>
                                    <?php endif; ?>
                                    <a href="<?= base_url('/artikel'); ?>" class="btn btn-outline-secondary btn-sm">
                                        <i class="fas fa-times"></i> Reset
                                    </a>
                                </div>
                            </div>
                        <?php endif; ?>
                    </form>
                </div>
            </div>
        </div>
    </div>



    <!-- SEMUA ARTIKEL -->
    <div class="row">
        <?php if (!empty($artikel)): ?>
            <?php foreach ($artikel as $row): ?>
                <div class="col-md-6 col-lg-4 mb-4">
                    <article class="card h-100 shadow-sm border-0">
                        <?php if (!empty($row['gambar'])): ?>
                            <img src="<?= base_url('/gambar/' . $row['gambar']); ?>"
                                alt="<?= esc($row['judul']); ?>"
                                class="card-img-top"
                                style="height: 250px; object-fit: cover;">
                        <?php else: ?>
                            <div class="card-img-top bg-light d-flex align-items-center justify-content-center"
                                style="height: 250px;">
                                <i class="fas fa-image text-muted fa-3x"></i>
                            </div>
                        <?php endif; ?>

                        <div class="card-body d-flex flex-column">
                            <h5 class="card-title mb-3">
                                <a href="<?= base_url('/artikel/' . $row['slug']); ?>"
                                    class="text-decoration-none text-dark">
                                    <?= esc($row['judul']); ?>
                                </a>
                            </h5>

                            <?php if (!empty($row['nama_kategori'])): ?>
                                <div class="mb-3">
                                    <span class="badge bg-primary">
                                        <i class="fas fa-tag"></i> <?= esc($row['nama_kategori']); ?>
                                    </span>
                                </div>
                            <?php endif; ?>

                            <p class="card-text text-muted mb-3">
                                <?= esc(substr(strip_tags($row['isi']), 0, 150)); ?>...
                            </p>

                            <div class="mt-auto">
                                <div class="d-flex justify-content-between align-items-center">
                                    <small class="text-muted">
                                        <i class="fas fa-calendar-alt"></i>
                                        <?= date('d M Y', strtotime($row['tanggal'] ?? date('Y-m-d'))); ?>
                                    </small>
                                    <a href="<?= base_url('/artikel/' . $row['slug']); ?>"
                                        class="btn btn-primary btn-sm">
                                        <i class="fas fa-arrow-right"></i> Baca
                                    </a>
                                </div>
                            </div>
                        </div>
                    </article>
                </div>
            <?php endforeach; ?>
        <?php else: ?>
            <div class="col-12">
                <div class="text-center py-5">
                    <i class="fas fa-newspaper fa-4x text-muted mb-3"></i>
                    <h4 class="text-muted">Belum ada artikel</h4>
                    <p class="text-muted">
                        <?php if (!empty($selected_kategori)): ?>
                            Tidak ada artikel dalam kategori yang dipilih.
                        <?php else: ?>
                            Belum ada artikel yang dipublikasikan.
                        <?php endif; ?>
                    </p>
                </div>
            </div>
        <?php endif; ?>
    </div>

    <?= $this->include('template/footer'); ?>