<?php

namespace App\Controllers;

use CodeIgniter\RESTful\ResourceController;
use CodeIgniter\API\ResponseTrait;
use App\Models\ArtikelModel;

class Post extends ResourceController
{
    use ResponseTrait;

    public function __construct()
    {
        // Add CORS headers
        header('Access-Control-Allow-Origin: *');
        header('Access-Control-Allow-Methods: GET, POST, PUT, DELETE, OPTIONS');
        header('Access-Control-Allow-Headers: Content-Type, Authorization');

        // Handle preflight OPTIONS request
        if ($_SERVER['REQUEST_METHOD'] === 'OPTIONS') {
            http_response_code(200);
            exit();
        }
    }

    // all users
    public function index()
    {
        $model = new ArtikelModel();
        // Return ALL data (both Draft and Publish)
        $data['artikel'] = $model->orderBy('id', 'DESC')->findAll();
        return $this->respond($data);
    }

    // create
    public function create()
    {
        $model = new ArtikelModel();

        // Get data from request - try multiple methods
        $input = null;

        try {
            $input = $this->request->getJSON(true); // Try JSON first
        } catch (\Exception $e) {
            log_message('debug', 'JSON parse failed: ' . $e->getMessage());
        }

        if ($input) {
            // JSON request
            $judul = $input['judul'] ?? '';
            $isi = $input['isi'] ?? '';
            $status = $input['status'] ?? 0;
        } else {
            // Try raw input
            $rawInput = file_get_contents('php://input');
            log_message('debug', 'Raw input: ' . $rawInput);

            if ($rawInput) {
                $input = json_decode($rawInput, true);
                if ($input) {
                    $judul = $input['judul'] ?? '';
                    $isi = $input['isi'] ?? '';
                    $status = $input['status'] ?? 0;
                } else {
                    // Form data request
                    $judul = $this->request->getVar('judul') ?: $this->request->getPost('judul');
                    $isi = $this->request->getVar('isi') ?: $this->request->getPost('isi');
                    $status = $this->request->getVar('status') ?: $this->request->getPost('status') ?: 0;
                }
            } else {
                // Form data request
                $judul = $this->request->getVar('judul') ?: $this->request->getPost('judul');
                $isi = $this->request->getVar('isi') ?: $this->request->getPost('isi');
                $status = $this->request->getVar('status') ?: $this->request->getPost('status') ?: 0;
            }
        }

        // Debug: log received data
        log_message('debug', 'POST Request - Judul: ' . $judul . ', Isi: ' . $isi . ', Status: ' . $status . ' (type: ' . gettype($status) . ')');
        log_message('debug', 'Content-Type: ' . $this->request->getHeaderLine('Content-Type'));
        log_message('debug', 'Raw Input: ' . $this->request->getBody());
        log_message('debug', 'JSON Input: ' . json_encode($input));

        // Validate input
        if (empty($judul)) {
            return $this->fail('Judul harus diisi.');
        }
        if (empty($isi)) {
            return $this->fail('Isi harus diisi.');
        }

        $data = [
            'judul' => $judul,
            'isi' => $isi,
            'slug' => url_title($judul, '-', true),
            'status' => (int)$status,
            'tanggal' => date('Y-m-d H:i:s')
        ];

        // Debug: log data yang akan diinsert
        log_message('debug', 'Data to insert: ' . json_encode($data));

        try {
            $insertResult = $model->insert($data);
            log_message('debug', 'Insert result: ' . ($insertResult ? 'SUCCESS' : 'FAILED'));
            $response = [
                'status' => 201,
                'error' => null,
                'messages' => [
                    'success' => 'Data artikel berhasil ditambahkan.'
                ]
            ];
            return $this->respondCreated($response);
        } catch (\Exception $e) {
            return $this->fail('Error inserting data: ' . $e->getMessage());
        }
    }

    // single user
    public function show($id = null)
    {
        $model = new ArtikelModel();
        $data = $model->where('id', $id)->first();
        if ($data) {
            return $this->respond($data);
        } else {
            return $this->failNotFound('Data tidak ditemukan.');
        }
    }

    // update
    public function update($id = null)
    {
        $model = new ArtikelModel();

        // Check if article exists
        $existingData = $model->find($id);
        if (!$existingData) {
            return $this->failNotFound('Data tidak ditemukan.');
        }

        // Get JSON input for PUT requests
        $input = $this->request->getJSON(true);

        if (!$input) {
            // Fallback to raw input
            $rawInput = file_get_contents('php://input');
            $input = json_decode($rawInput, true);
        }

        $judul = $input['judul'] ?? '';
        $isi = $input['isi'] ?? '';
        $status = $input['status'] ?? 0;

        // Log untuk debug
        log_message('debug', 'PUT Request ID: ' . $id);
        log_message('debug', 'PUT Data: ' . json_encode($input));

        // Validate input
        if (empty($judul) || empty($isi)) {
            return $this->fail('Judul dan isi harus diisi.');
        }

        $data = [
            'judul' => $judul,
            'isi' => $isi,
            'slug' => url_title($judul, '-', true),
            'status' => (int)$status
        ];

        try {
            $model->update($id, $data);
            $response = [
                'status' => 200,
                'error' => null,
                'messages' => [
                    'success' => 'Data artikel berhasil diubah.'
                ]
            ];
            return $this->respond($response);
        } catch (\Exception $e) {
            return $this->fail('Error updating data: ' . $e->getMessage());
        }
    }

    // delete
    public function delete($id = null)
    {
        $model = new ArtikelModel();
        $data = $model->where('id', $id)->delete($id);
        if ($data) {
            $model->delete($id);
            $response = [
                'status' => 200,
                'error' => null,
                'messages' => [
                    'success' => 'Data artikel berhasil dihapus.'
                ]
            ];
            return $this->respondDeleted($response);
        } else {
            return $this->failNotFound('Data tidak ditemukan.');
        }
    }

    // API endpoint untuk kategori
    public function kategori()
    {
        $kategoriModel = new \App\Models\KategoriModel();
        $kategori = $kategoriModel->findAll();

        return $this->response->setJSON([
            'status' => 'success',
            'kategori' => $kategori
        ]);
    }
}
