<?= $this->include('template/admin_header'); ?>

<div class="container-fluid py-4">
    <!-- Header Section -->
    <div class="row mb-4">
        <div class="col-md-8">
            <h1 class="h3 mb-2">
                <i class="fas fa-tachometer-alt me-2 text-primary"></i><?= $title; ?>
            </h1>
            <p class="text-muted">Kelola artikel dengan mudah menggunakan fitur pencarian dan filter</p>
        </div>
        <div class="col-md-4 text-md-end">
            <a href="<?= base_url('/admin/artikel/add'); ?>" class="btn btn-success">
                <i class="fas fa-plus me-2"></i>Tambah Artikel Baru
            </a>
        </div>
    </div>

    <!-- Success/Error Messages -->
    <?php if (session()->getFlashdata('success')): ?>
        <div class="alert alert-success alert-dismissible fade show" role="alert">
            <i class="fas fa-check-circle me-2"></i>
            <?= session()->getFlashdata('success'); ?>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <?php if (session()->getFlashdata('errors')): ?>
        <div class="alert alert-danger alert-dismissible fade show" role="alert">
            <i class="fas fa-exclamation-triangle me-2"></i>
            <strong>Terdapat kesalahan:</strong>
            <ul class="mb-0 mt-2">
                <?php foreach (session()->getFlashdata('errors') as $error): ?>
                    <li><?= $error; ?></li>
                <?php endforeach; ?>
            </ul>
            <button type="button" class="btn-close" data-bs-dismiss="alert"></button>
        </div>
    <?php endif; ?>

    <!-- Search & Filter Section -->
    <div class="card shadow-sm mb-4">
        <div class="card-header bg-light">
            <h5 class="mb-0">
                <i class="fas fa-search me-2"></i>Pencarian & Filter
            </h5>
        </div>
        <div class="card-body">
            <form id="search-form" class="row g-3">
                <div class="col-md-6">
                    <label for="search-box" class="form-label">Cari Artikel</label>
                    <div class="input-group">
                        <span class="input-group-text"><i class="fas fa-search"></i></span>
                        <input type="text" name="q" id="search-box" value="<?= $q; ?>"
                            placeholder="Cari berdasarkan judul artikel..." class="form-control">
                    </div>
                </div>
                <div class="col-md-4">
                    <label for="category-filter" class="form-label">Filter Kategori</label>
                    <select name="kategori_id" id="category-filter" class="form-select">
                        <option value="">Semua Kategori</option>
                        <?php foreach ($kategori as $k): ?>
                            <option value="<?= $k['id_kategori']; ?>" <?= ($kategori_id == $k['id_kategori']) ? 'selected' : ''; ?>>
                                <?= $k['nama_kategori']; ?>
                            </option>
                        <?php endforeach; ?>
                    </select>
                </div>
                <div class="col-md-2">
                    <label class="form-label">&nbsp;</label>
                    <div class="d-grid">
                        <button type="submit" class="btn btn-primary">
                            <i class="fas fa-search me-2"></i>Cari
                        </button>
                    </div>
                </div>
            </form>
        </div>
    </div>

    <!-- Articles Table Section -->
    <div class="card shadow-sm">
        <div class="card-header bg-primary text-white">
            <h5 class="mb-0">
                <i class="fas fa-list me-2"></i>Daftar Artikel
            </h5>
        </div>
        <div class="card-body p-0">
            <div id="article-container">
                <!-- Articles will be loaded here via AJAX -->
            </div>
        </div>
    </div>

    <!-- Pagination Section -->
    <div class="mt-4">
        <div id="pagination-container">
            <!-- Pagination will be loaded here via AJAX -->
        </div>
    </div>
</div>
<script src="https://code.jquery.com/jquery-3.6.0.min.js"></script>
<script>
    $(document).ready(function() {
        const articleContainer = $('#article-container');
        const paginationContainer = $('#pagination-container');
        const searchForm = $('#search-form');
        const searchBox = $('#search-box');
        const categoryFilter = $('#category-filter');

        const fetchData = (url) => {
            $.ajax({
                url: url,
                type: 'GET',
                dataType: 'json',
                headers: {
                    'X-Requested-With': 'XMLHttpRequest'
                },
                success: function(data) {
                    renderArticles(data.artikel);
                    if (data.pager && data.pager.links) {
                        renderPagination(data.pager, data.q, data.kategori_id);
                    }
                },
                error: function(xhr, status, error) {
                    console.error('AJAX Error:', error);
                    articleContainer.html('<div class="alert alert-danger">Error loading data: ' + error + '</div>');
                }
            });
        };

        const renderArticles = (articles) => {
            let html = '<div class="table-responsive">';
            html += '<table class="table table-hover mb-0">';
            html += '<thead class="table-light">';
            html += '<tr>';
            html += '<th width="5%">ID</th>';
            html += '<th width="40%">Artikel</th>';
            html += '<th width="15%">Kategori</th>';
            html += '<th width="15%">Status</th>';
            html += '<th width="15%">Tanggal</th>';
            html += '<th width="10%">Aksi</th>';
            html += '</tr>';
            html += '</thead><tbody>';

            if (articles && articles.length > 0) {
                articles.forEach(article => {
                    const isi = article.isi || '';
                    const isiPreview = isi.length > 100 ? isi.substring(0, 100) + '...' : isi;
                    const statusBadge = article.status == 1 ?
                        '<span class="badge bg-success">Published</span>' :
                        '<span class="badge bg-warning">Draft</span>';

                    html += `
                <tr>
                    <td><span class="badge bg-secondary">#${article.id}</span></td>
                    <td>
                        <div class="d-flex align-items-start">
                            ${article.gambar ?
                                `<img src="<?= base_url(); ?>/gambar/${article.gambar}" class="me-3 rounded" style="width: 50px; height: 50px; object-fit: cover;">` :
                                '<div class="me-3 bg-light rounded d-flex align-items-center justify-content-center" style="width: 50px; height: 50px;"><i class="fas fa-image text-muted"></i></div>'
                            }
                            <div>
                                <h6 class="mb-1">${article.judul}</h6>
                                <small class="text-muted">${isiPreview}</small>
                            </div>
                        </div>
                    </td>
                    <td>
                        <span class="badge bg-primary">${article.nama_kategori || 'Tidak ada kategori'}</span>
                    </td>
                    <td>${statusBadge}</td>
                    <td>
                        <small class="text-muted">
                            <i class="fas fa-calendar-alt me-1"></i>
                            ${article.tanggal || '-'}
                        </small>
                    </td>
                    <td>
                        <div class="btn-group" role="group">
                            <a class="btn btn-sm btn-outline-primary" href="<?= base_url('/admin/artikel/edit/'); ?>${article.id}" title="Edit">
                                <i class="fas fa-edit"></i>
                            </a>
                            <a class="btn btn-sm btn-outline-danger" onclick="return confirm('Yakin menghapus data?');" href="<?= base_url('/admin/artikel/delete/'); ?>${article.id}" title="Hapus">
                                <i class="fas fa-trash"></i>
                            </a>
                        </div>
                    </td>
                </tr>
                `;
                });
            } else {
                html += '<tr><td colspan="6" class="text-center py-4">';
                html += '<div class="text-muted">';
                html += '<i class="fas fa-inbox fa-3x mb-3"></i>';
                html += '<h5>Tidak ada artikel</h5>';
                html += '<p>Belum ada artikel yang sesuai dengan pencarian Anda.</p>';
                html += '</div>';
                html += '</td></tr>';
            }
            html += '</tbody></table></div>';
            articleContainer.html(html);
        };

        const renderPagination = (pager, q, kategori_id) => {
            let html = '<nav aria-label="Page navigation">';
            html += '<ul class="pagination justify-content-center">';
            pager.links.forEach(link => {
                let url = link.url ? `${link.url}&q=${q}&kategori_id=${kategori_id}` : '#';
                let disabled = !link.url ? 'disabled' : '';
                let active = link.active ? 'active' : '';

                html += `<li class="page-item ${active} ${disabled}">`;
                html += `<a class="page-link" href="${url}" data-url="${url}">${link.title}</a>`;
                html += '</li>';
            });
            html += '</ul></nav>';
            paginationContainer.html(html);

            // Add click event for pagination links
            paginationContainer.find('.page-link').on('click', function(e) {
                e.preventDefault();
                const url = $(this).data('url');
                if (url && url !== '#') {
                    fetchData(url);
                }
            });
        };

        searchForm.on('submit', function(e) {
            e.preventDefault();
            const q = searchBox.val();
            const kategori_id = categoryFilter.val();
            fetchData(`<?= base_url(); ?>/admin/artikel?q=${q}&kategori_id=${kategori_id}`);
        });

        categoryFilter.on('change', function() {
            searchForm.trigger('submit');
        });

        // Initial load
        fetchData('<?= base_url(); ?>/admin/artikel');
    });
</script>
<?= $this->include('template/admin_footer'); ?>