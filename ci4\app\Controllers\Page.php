<?php

namespace App\Controllers;

use App\Models\ArtikelModel;
use CodeIgniter\Exceptions\PageNotFoundException;

class Page extends BaseController
{
    public function about()
    {
        $artikelModel = new ArtikelModel();
        $kategoriModel = new \App\Models\KategoriModel();

        // Hitung statistik
        $total_artikel = $artikelModel->where('status', 1)->countAllResults(); // <PERSON><PERSON> yang publish
        $total_kategori = $kategoriModel->countAllResults();

        return view('about', [
            'title' => 'Tentang Kami',
            'content' => 'Selamat datang di platform berbagi pengetahuan dan informasi terdepan.',
            'total_artikel' => $total_artikel,
            'total_kategori' => $total_kategori
        ]);
    }

    public function contact()
    {
        $data = [
            'title' => '📞 Halaman Kontak',
            'content' => 'Hubungi kami untuk informasi lebih lanjut atau jika Anda memiliki pertanyaan.',
            'success' => false,
            'error' => false
        ];

        // Jika ada POST request (form disubmit)
        if ($this->request->getMethod() === 'post') {
            $validation = \Config\Services::validation();

            $validation->setRules([
                'nama' => 'required|min_length[3]',
                'email' => 'required|valid_email',
                'subjek' => 'required|min_length[5]',
                'pesan' => 'required|min_length[10]'
            ]);

            if ($validation->withRequest($this->request)->run()) {
                // Data valid, bisa disimpan ke database atau kirim email
                // Untuk saat ini, kita hanya tampilkan pesan sukses
                $data['success'] = true;
                $data['message'] = 'Terima kasih! Pesan Anda telah berhasil dikirim. Kami akan segera menghubungi Anda.';
            } else {
                $data['error'] = true;
                $data['errors'] = $validation->getErrors();
            }
        }

        return view('contact', $data);
    }

    public function faqs()
    {
        echo "Ini halaman FAQ";
    }

    public function tos()
    {
        echo "Ini halaman Term of Services";
    }

    public function services()
    {
        return view('services', [
            'title' => '💼 Halaman Services',
            'content' => 'Kami menyediakan berbagai layanan, mulai dari konsultasi IT hingga pengembangan software. Hubungi kami untuk informasi lebih lanjut!'
        ]);
    }

    public function artikel()
    {
        $model = new ArtikelModel();
        $artikel = $model->findAll(); // Ambil semua data artikel dari database

        return view('artikel/index', [
            'title' => '📰 Halaman Artikel',
            'artikel' => $artikel // Kirim data ke tampilan
        ]);
    }


    public function view($slug)
    {
        $model = new ArtikelModel();
        $artikel = $model->where(['slug' => $slug])->first();

        // Menampilkan error apabila data tidak ada.
        if (!$artikel) {
            throw PageNotFoundException::forPageNotFound();
        }

        $title = $artikel['judul'];
        return view('artikel/detail', compact('artikel', 'title'));
    }
}
